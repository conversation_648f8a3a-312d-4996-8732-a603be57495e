# API Routes Documentation

## Overview

This document provides comprehensive documentation for all available API routes in the FastAPI application. The routing system is modular and follows the pattern from your constructor example, providing all possible routes that can be used inside the app or from external projects.

## Base URL

- **Development**: `http://localhost:8000`
- **Production**: `http://your-domain.com`

## Route Categories

### 1. OF (Ordre de Fabrication) Routes - `/api/of`

Based on your constructor example, these routes provide access to production order data.

#### GET `/api/of/en_cours`
- **Description**: Get OF en cours (current production orders)
- **Response**: List of current production orders with calculated advancement
- **Example**: `GET /api/of/en_cours`

#### GET `/api/of/histo`
- **Description**: Get OF historique (historical production orders)
- **Response**: List of historical production orders
- **Example**: `GET /api/of/histo`

#### GET `/api/of/all`
- **Description**: Get all OF data (combined en_cours and histo)
- **Response**: Combined data from both current and historical orders
- **Example**: `GET /api/of/all`

#### GET `/api/of/filtered`
- **Description**: Get filtered OF data with advanced filtering
- **Parameters**:
  - `date_debut` (optional): Start date (YYYY-MM-DD)
  - `date_fin` (optional): End date (YYYY-MM-DD)
  - `statut_filter` (optional): Status filter (C/T/A)
  - `famille_filter` (optional): Family filter
  - `client_filter` (optional): Client filter
  - `alerte_filter` (optional): Alert filter (boolean)
- **Example**: `GET /api/of/filtered?statut_filter=C&date_debut=2024-01-01`

#### GET `/api/of/by_status/{status}`
- **Description**: Get OF data by specific status
- **Parameters**:
  - `status` (path): Status code (C, T, A)
- **Example**: `GET /api/of/by_status/C`

### 2. Dashboard Routes - `/`

Main dashboard and summary data routes.

#### GET `/`
- **Description**: Main dashboard HTML page
- **Response**: HTML dashboard interface

#### GET `/api/dashboard-data`
- **Description**: Get complete dashboard data including KPIs
- **Parameters**:
  - `date_debut` (optional): Start date (YYYY-MM-DD)
  - `date_fin` (optional): End date (YYYY-MM-DD)
  - `statut_filter` (optional): Status filter (C/T/A)
- **Example**: `GET /api/dashboard-data?date_debut=2024-01-01&date_fin=2024-12-31`

#### GET `/api/kpis`
- **Description**: Get KPIs only for dashboard widgets
- **Parameters**: Same as dashboard-data
- **Example**: `GET /api/kpis`

#### GET `/api/summary-stats`
- **Description**: Get summary statistics for the dashboard
- **Example**: `GET /api/summary-stats`

#### GET `/api/filters/options`
- **Description**: Get available filter options for dropdowns
- **Example**: `GET /api/filters/options`

### 3. Charge de Travail Routes - `/api/charge`

Workload and capacity analysis routes.

#### GET `/api/charge/`
- **Description**: Get workload analysis data
- **Parameters**:
  - `date_debut` (optional): Start date
  - `date_fin` (optional): End date
- **Example**: `GET /api/charge/?date_debut=2024-01-01`

#### GET `/api/charge/by_sector`
- **Description**: Get workload data grouped by sector
- **Example**: `GET /api/charge/by_sector`

#### GET `/api/charge/capacity`
- **Description**: Get capacity analysis based on personnel data
- **Example**: `GET /api/charge/capacity`

#### GET `/api/charge/overload`
- **Description**: Get sectors with workload above threshold
- **Parameters**:
  - `threshold` (optional): Overload threshold percentage (default: 100.0)
- **Example**: `GET /api/charge/overload?threshold=120`

#### GET `/api/charge/efficiency`
- **Description**: Get efficiency analysis by personnel qualification
- **Example**: `GET /api/charge/efficiency`

#### GET `/api/charge/planning`
- **Description**: Get planning data for workload distribution
- **Example**: `GET /api/charge/planning`

### 4. Backlog Routes - `/api/backlog`

Production order backlog management routes.

#### GET `/api/backlog/`
- **Description**: Get backlog data
- **Parameters**:
  - `date_debut` (optional): Start date
  - `date_fin` (optional): End date
- **Example**: `GET /api/backlog/`

#### GET `/api/backlog/urgent`
- **Description**: Get urgent backlog items (overdue orders)
- **Example**: `GET /api/backlog/urgent`

#### GET `/api/backlog/by_priority/{priority}`
- **Description**: Get backlog items by priority
- **Parameters**:
  - `priority` (path): URGENT, PRIORITAIRE, or NORMAL
- **Example**: `GET /api/backlog/by_priority/URGENT`

#### GET `/api/backlog/by_client/{client}`
- **Description**: Get backlog items by client
- **Parameters**:
  - `client` (path): Client name
- **Example**: `GET /api/backlog/by_client/ClientName`

#### GET `/api/backlog/summary`
- **Description**: Get backlog summary statistics
- **Example**: `GET /api/backlog/summary`

#### GET `/api/backlog/overdue`
- **Description**: Get overdue production orders
- **Example**: `GET /api/backlog/overdue`

### 5. Personnel Routes - `/api/personnel`

Employee and workforce management routes.

#### GET `/api/personnel/`
- **Description**: Get all active personnel data
- **Example**: `GET /api/personnel/`

#### GET `/api/personnel/by_sector/{sector}`
- **Description**: Get personnel data by sector
- **Parameters**:
  - `sector` (path): CMS, ASSEMBLAGE, CABLAGE, TEST, or GENERAL
- **Example**: `GET /api/personnel/by_sector/CMS`

#### GET `/api/personnel/qualifications`
- **Description**: Get all unique qualifications
- **Example**: `GET /api/personnel/qualifications`

#### GET `/api/personnel/efficiency`
- **Description**: Get efficiency coefficients by qualification level
- **Example**: `GET /api/personnel/efficiency`

#### GET `/api/personnel/summary`
- **Description**: Get personnel summary statistics
- **Example**: `GET /api/personnel/summary`

#### GET `/api/personnel/search`
- **Description**: Search personnel by various criteria
- **Parameters**:
  - `nom` (optional): Search by last name
  - `prenom` (optional): Search by first name
  - `qualification` (optional): Search by qualification
  - `secteur` (optional): Search by sector
- **Example**: `GET /api/personnel/search?nom=Dupont&secteur=CMS`

### 6. Export Routes - `/api/export`

Data export functionality routes.

#### GET `/api/export/csv/of`
- **Description**: Export OF data as CSV
- **Parameters**: Same as filtered OF data
- **Example**: `GET /api/export/csv/of?statut_filter=C`

#### GET `/api/export/csv/charge`
- **Description**: Export charge data as CSV
- **Example**: `GET /api/export/csv/charge`

#### GET `/api/export/csv/backlog`
- **Description**: Export backlog data as CSV
- **Example**: `GET /api/export/csv/backlog`

#### GET `/api/export/csv/personnel`
- **Description**: Export personnel data as CSV
- **Example**: `GET /api/export/csv/personnel`

#### GET `/api/export/csv/dashboard`
- **Description**: Export complete dashboard data as CSV
- **Example**: `GET /api/export/csv/dashboard`

#### GET `/api/export/report/text`
- **Description**: Generate and export a text report
- **Example**: `GET /api/export/report/text`

#### GET `/api/export/formats`
- **Description**: Get available export formats and their descriptions
- **Example**: `GET /api/export/formats`

### 7. Health & Status Routes - `/api/health`

System health and monitoring routes.

#### GET `/api/health/`
- **Description**: Basic health check endpoint
- **Example**: `GET /api/health/`

#### GET `/api/health/detailed`
- **Description**: Detailed health check with system information
- **Example**: `GET /api/health/detailed`

#### GET `/api/health/database`
- **Description**: Check database connectivity
- **Example**: `GET /api/health/database`

#### GET `/api/health/data-sources`
- **Description**: Check availability of main data sources
- **Example**: `GET /api/health/data-sources`

#### GET `/api/health/performance`
- **Description**: Check system performance with timing tests
- **Example**: `GET /api/health/performance`

#### GET `/api/health/version`
- **Description**: Get application version and build information
- **Example**: `GET /api/health/version`

## Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": {
    // Response data here
  },
  "message": "Optional message",
  "count": 123  // For list responses
}
```

## Error Handling

Error responses follow this format:

```json
{
  "success": false,
  "error": "Error description",
  "detail": "Detailed error information"
}
```

## Authentication

Currently, no authentication is required. This can be added later using FastAPI's security features.

## Rate Limiting

No rate limiting is currently implemented. Consider adding this for production use.

## API Documentation

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Usage Examples

### External Project Integration

```python
import requests

# Get current production orders
response = requests.get("http://localhost:8000/api/of/en_cours")
of_data = response.json()

# Get dashboard KPIs
response = requests.get("http://localhost:8000/api/kpis")
kpis = response.json()

# Export data as CSV
response = requests.get("http://localhost:8000/api/export/csv/of")
with open("of_data.csv", "wb") as f:
    f.write(response.content)
```

### JavaScript/Frontend Integration

```javascript
// Fetch dashboard data
fetch('/api/dashboard-data')
  .then(response => response.json())
  .then(data => {
    console.log('Dashboard data:', data);
  });

// Get filtered OF data
const params = new URLSearchParams({
  statut_filter: 'C',
  date_debut: '2024-01-01'
});
fetch(`/api/of/filtered?${params}`)
  .then(response => response.json())
  .then(data => {
    console.log('Filtered OF data:', data);
  });
```

## Notes

- All date parameters should be in YYYY-MM-DD format
- Boolean parameters accept `true`/`false` or `1`/`0`
- The API is designed to be RESTful and follows FastAPI best practices
- All routes support CORS for cross-origin requests
- Response times are optimized for production use
