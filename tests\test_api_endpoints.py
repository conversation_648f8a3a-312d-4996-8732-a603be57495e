"""
Test API endpoints for the FastAPI application.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, <PERSON><PERSON>
import json


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_basic_health_check(self, client):
        """Test basic health check endpoint."""
        response = client.get("/api/health/")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "status" in data["data"]
        assert "timestamp" in data["data"]
    
    def test_database_health_check_success(self, client, mock_analyzer):
        """Test database health check with successful connection."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/health/database")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["data"]["database_status"] == "connected"
    
    def test_performance_check(self, client, mock_analyzer):
        """Test performance check endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/health/performance")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "simple_query" in data["data"]


class TestDashboardEndpoints:
    """Test dashboard-related endpoints."""
    
    def test_dashboard_route(self, client):
        """Test main dashboard route."""
        response = client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_dashboard_data_endpoint(self, client, mock_analyzer):
        """Test dashboard data API endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/dashboard-data")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "data" in data
            assert "kpis" in data
    
    def test_dashboard_data_with_filters(self, client, mock_analyzer):
        """Test dashboard data with query parameters."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            params = {
                "date_debut": "2024-01-01",
                "date_fin": "2024-12-31",
                "statut_filter": "C"
            }
            response = client.get("/api/dashboard-data", params=params)
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
    
    def test_kpis_endpoint(self, client, mock_analyzer):
        """Test KPIs endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/kpis")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "total_of" in data["data"]
    
    def test_filters_options_endpoint(self, client, mock_analyzer):
        """Test filter options endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/filters/options")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "options" in data["data"]


class TestOFEndpoints:
    """Test OF (Order of Fabrication) endpoints."""
    
    def test_of_all_endpoint(self, client, mock_analyzer):
        """Test get all OF endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/of/all")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert isinstance(data["data"], list)
    
    def test_of_en_cours_endpoint(self, client, mock_analyzer):
        """Test get OF en cours endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/of/en_cours")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
    
    def test_of_by_status_endpoint(self, client, mock_analyzer):
        """Test get OF by status endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/of/by_status?status=C")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True


class TestChargeEndpoints:
    """Test charge (workload) endpoints."""
    
    def test_charge_by_sector_endpoint(self, client, mock_analyzer):
        """Test charge by sector endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/charge/by_sector")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
    
    def test_charge_capacity_endpoint(self, client, mock_analyzer):
        """Test charge capacity endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/charge/capacity")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True


class TestBacklogEndpoints:
    """Test backlog endpoints."""
    
    def test_backlog_urgent_endpoint(self, client, mock_analyzer):
        """Test urgent backlog endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/backlog/urgent")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
    
    def test_backlog_by_priority_endpoint(self, client, mock_analyzer):
        """Test backlog by priority endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/backlog/by_priority")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True


class TestPersonnelEndpoints:
    """Test personnel endpoints."""
    
    def test_personnel_by_sector_endpoint(self, client, mock_analyzer):
        """Test personnel by sector endpoint."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            response = client.get("/api/personnel/by_sector")
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True


class TestErrorHandling:
    """Test error handling scenarios."""
    
    def test_database_connection_error(self, client):
        """Test behavior when database connection fails."""
        with patch('app.core.database.get_analyzer', side_effect=Exception("Database connection failed")):
            response = client.get("/api/dashboard-data")
            # Should handle gracefully, not crash
            assert response.status_code in [200, 500]  # Depending on error handling implementation
    
    def test_invalid_endpoint(self, client):
        """Test accessing non-existent endpoint."""
        response = client.get("/api/nonexistent")
        assert response.status_code == 404
    
    def test_invalid_query_parameters(self, client, mock_analyzer):
        """Test with invalid query parameters."""
        with patch('app.core.database.get_analyzer', return_value=mock_analyzer):
            params = {
                "date_debut": "invalid-date",
                "date_fin": "also-invalid"
            }
            response = client.get("/api/dashboard-data", params=params)
            # Should handle gracefully
            assert response.status_code in [200, 400]
