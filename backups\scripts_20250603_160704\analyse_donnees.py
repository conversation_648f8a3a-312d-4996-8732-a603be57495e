#!/usr/bin/env python3
"""
Script d'analyse des données pour l'application de suivi du temps de production
basée entièrement sur Excalibur ERP.
Version améliorée avec gestion des configurations et sécurité.
"""

import pandas as pd
import numpy as np
import pyodbc
from datetime import datetime, timedelta
import warnings
import os
from dotenv import load_dotenv
from typing import Dict, Any, Optional, List, Tuple

warnings.filterwarnings('ignore', category=UserWarning, module='pyodbc') # Be more specific
load_dotenv()

class ExcaliburDataAnalyzer:
    """
    Classe pour analyser les données d'Excalibur ERP de manière autonome.
    Gère la connexion à la base de données et l'exécution des requêtes.
    """

    def __init__(self):
        # Get connection parameters from environment
        self.db_uid = os.getenv('DB_UID')
        self.db_pwd = os.getenv('DB_PWD')
        self.db_host = os.getenv('DB_HOST')
        self.db_server_name = os.getenv('DB_SERVER_NAME')
        self.db_database_name = os.getenv('DB_DATABASE_NAME')

        if not all([self.db_uid, self.db_pwd, self.db_host, self.db_server_name, self.db_database_name]):
            raise ValueError("Paramètres de connexion à la base de données manquants dans .env")

        # Build PyODBC connection string
        self.connection_string = (
            f"DRIVER={{SQL Anywhere 17}};"
            f"SERVER={self.db_server_name};"
            f"HOST={self.db_host};"
            f"DATABASE={self.db_database_name};"
            f"UID={self.db_uid};"
            f"PWD={self.db_pwd};"
            f"CHARSET=UTF-8;"
        )
        self.conn: Optional[pyodbc.Connection] = None

    def _connect(self) -> bool:
        """Établit la connexion à la base de données si elle n'existe pas."""
        if self.conn is None:
            try:
                self.conn = pyodbc.connect(self.connection_string)
                return True
            except pyodbc.Error as e:
                print(f"Erreur PyODBC lors de la connexion: {e}")
                self.conn = None
                return False
            except Exception as e:
                print(f"Erreur inattendue lors de la connexion: {e}")
                self.conn = None
                return False
        return True

    def _close_connection(self) -> None:
        """Ferme la connexion à la base de données."""
        if self.conn:
            try:
                self.conn.close()
            except:
                pass  # Ignore errors when closing
            self.conn = None # Ensure it's reset

    def execute_query(self, query: str, params: Optional[Tuple[Any, ...]] = None) -> Optional[pd.DataFrame]:
        """Exécute une requête SQL et retourne un DataFrame."""
        if not self._connect():
            return None

        try:
            cursor = self.conn.cursor() # type: ignore
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            # Get column names
            columns = [column[0] for column in cursor.description]

            # Fetch all data
            data = cursor.fetchall()

            # Convert PyODBC Row objects to regular tuples/lists for pandas
            if data:
                # Convert each row to a list to ensure pandas can handle it properly
                data_list = [list(row) for row in data]
                df = pd.DataFrame(data_list, columns=columns)
            else:
                # Create empty DataFrame with correct columns
                df = pd.DataFrame(columns=columns)

            return df
        except pyodbc.Error as e:
            print(f"Erreur lors de l'exécution de la requête: {e}")
            return None
        except Exception as e:
            print(f"Erreur inattendue lors de l'exécution de la requête: {e}")
            return None
        finally:
            # Decide on connection closing strategy. For frequent calls, might keep open.
            # For this script structure, closing after each top-level call is safer.
            # The main methods will handle this.
            pass

    def get_comprehensive_of_data(self, date_debut: Optional[str] = None,
                                   date_fin: Optional[str] = None,
                                   statut_filter: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Récupère les données complètes des OF avec tous les calculs intégrés en SQL."""
        query_base = """
        SELECT
            OF_DA.NUMERO_OFDA,
            OF_DA.PRODUIT,
            OF_DA.STATUT,
            OF_DA.LANCEMENT_AU_PLUS_TARD,
            OF_DA.QUANTITE_DEMANDEE,
            OF_DA.CUMUL_ENTREES,
            OF_DA.DUREE_PREVUE,
            OF_DA.CUMUL_TEMPS_PASSES,
            OF_DA.AFFAIRE,
            OF_DA.DESIGNATION,
            COALESCE(OF_DA.CLIENT, 'N/A') AS CLIENT,
            -- Assumons que FAMILLE_TECHNIQUE existe dans OF_DA ou est joignable
            -- Si ce n'est pas le cas, cette ligne doit être adaptée ou supprimée.
            COALESCE(OF_DA.FAMILLE_TECHNIQUE, 'INCONNUE') AS FAMILLE_TECHNIQUE,

            DATEPART(week, OF_DA.LANCEMENT_AU_PLUS_TARD) AS SEMAINE,
            CASE
                WHEN OF_DA.QUANTITE_DEMANDEE > 0
                THEN CAST(OF_DA.CUMUL_ENTREES AS FLOAT) / CAST(OF_DA.QUANTITE_DEMANDEE AS FLOAT)
                ELSE 0.0
            END AS Avancement_PROD,
            CASE
                WHEN OF_DA.DUREE_PREVUE > 0
                THEN CAST(OF_DA.CUMUL_TEMPS_PASSES AS FLOAT) / CAST(OF_DA.DUREE_PREVUE AS FLOAT)
                ELSE 0.0
            END AS Avancement_temps,
            CASE
                WHEN OF_DA.DUREE_PREVUE > 0 AND OF_DA.CUMUL_TEMPS_PASSES > OF_DA.DUREE_PREVUE
                THEN 1 -- True
                ELSE 0 -- False
            END AS Alerte_temps_raw, -- Renamed for clarity
            (OF_DA.QUANTITE_DEMANDEE - COALESCE(OF_DA.CUMUL_ENTREES, 0)) AS QUANTITE_RESTANTE,
            CASE
                WHEN OF_DA.CUMUL_TEMPS_PASSES > 0
                THEN CAST(OF_DA.DUREE_PREVUE AS FLOAT) / CAST(OF_DA.CUMUL_TEMPS_PASSES AS FLOAT)
                ELSE 0.0
            END AS EFFICACITE
        FROM gpao.OF_DA OF_DA
        WHERE OF_DA.NUMERO_OFDA LIKE 'F%' -- Filtre de base
        """
        conditions: List[str] = []
        params_list: List[Any] = []

        if date_debut and date_fin:
            conditions.append("OF_DA.LANCEMENT_AU_PLUS_TARD BETWEEN ? AND ?")
            params_list.extend([date_debut, date_fin])
        if statut_filter:
            conditions.append("OF_DA.STATUT = ?")
            params_list.append(statut_filter)

        query = query_base
        if conditions:
            query += " AND " + " AND ".join(conditions)
        query += " ORDER BY OF_DA.LANCEMENT_AU_PLUS_TARD DESC, OF_DA.NUMERO_OFDA"

        df = self.execute_query(query, tuple(params_list) if params_list else None)
        if df is not None:
            # Convert Alerte_temps_raw to boolean
            df['Alerte_temps'] = df['Alerte_temps_raw'].astype(bool)
            df.drop(columns=['Alerte_temps_raw'], inplace=True)
        self._close_connection() # Close after top-level operation
        return df

    def get_personnel_data(self) -> Optional[pd.DataFrame]:
        query = """
        SELECT
            SALARIES.NOM,
            SALARIES.PRENOM,
            COALESCE(SALARIES.QUALIFICATION, 'N/A') AS QUALIFICATION,
            SALARIES.ACTIF,
            (SALARIES.NOM + ' ' + COALESCE(SALARIES.PRENOM, '')) AS NOM_COMPLET,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%EXPERT%' THEN 1.2
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%SENIOR%' THEN 1.1
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%JUNIOR%' THEN 0.9
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 0.7
                ELSE 1.0
            END AS COEFFICIENT_EFFICACITE,
            CASE
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(SALARIES.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END AS SECTEUR_PERSONNEL -- Renamed for clarity
        FROM gpao.SALARIES SALARIES
        WHERE SALARIES.ACTIF = 1
        ORDER BY SALARIES.NOM, SALARIES.PRENOM
        """
        df = self.execute_query(query)
        self._close_connection()
        return df

    def get_charge_travail_data(self, date_debut: Optional[str] = None,
                                 date_fin: Optional[str] = None) -> Optional[pd.DataFrame]:
        # This query is complex and highly dependent on how workload is assigned to sectors.
        # Option 1: Based on personnel's sector and their availability.
        # Option 2: Based on OF's workcenter/sector and summing up remaining hours.
        # The original query was simplistic. Let's try to make it more meaningful
        # by combining personnel capacity and OF load.

        # For simplicity, we'll calculate available hours by personnel sector.
        # Then, we'll fetch OF remaining hours and try to allocate them if a clear link exists.
        # If OFs don't have a direct SECTEUR, this becomes an estimation.

        personnel_by_sector_query = """
        SELECT
            CASE
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%CMS%' THEN 'CMS'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%ASSEMBLAGE%' THEN 'ASSEMBLAGE'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%CABLE%' THEN 'CABLAGE'
                WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%TEST%' THEN 'TEST'
                ELSE 'GENERAL'
            END AS SECTEUR,
            COUNT(CASE WHEN UPPER(COALESCE(S.QUALIFICATION, '')) NOT LIKE '%APPRENTI%' THEN 1 END) AS NB_OPERATEURS,
            COUNT(CASE WHEN UPPER(COALESCE(S.QUALIFICATION, '')) LIKE '%APPRENTI%' THEN 1 END) AS NB_APPRENTIS,
            (COUNT(*) * 40) AS NB_HEURES_DISPONIBLES_SEMAINE -- Assuming 40h/week
        FROM gpao.SALARIES S
        WHERE S.ACTIF = 1
        GROUP BY SECTEUR
        """
        df_personnel_charge = self.execute_query(personnel_by_sector_query)

        # Get total remaining planned hours for OFs in status 'C' (En Cours)
        # This assumes OF_DA.SECTEUR_OF exists or can be derived (e.g., from OF_DA.PRODUIT or a linked work center)
        # If not, we can only get a global load.
        # For this example, let's assume FAMILLE_TECHNIQUE can act as a proxy for SECTEUR_OF
        of_load_query_base = """
        SELECT
            COALESCE(OF_DA.FAMILLE_TECHNIQUE, 'GENERAL') AS SECTEUR_OF,
            SUM(CASE
                WHEN OF_DA.DUREE_PREVUE > OF_DA.CUMUL_TEMPS_PASSES
                THEN (OF_DA.DUREE_PREVUE - OF_DA.CUMUL_TEMPS_PASSES)
                ELSE 0
            END) AS HEURES_PLANIFIEES_RESTANTES,
            COUNT(*) AS NB_OF_EN_COURS_SECTEUR
        FROM gpao.OF_DA OF_DA
        WHERE OF_DA.STATUT = 'C' AND OF_DA.NUMERO_OFDA LIKE 'F%'
        """
        conditions_of: List[str] = []
        params_list_of: List[Any] = []
        if date_debut and date_fin:
            conditions_of.append("OF_DA.LANCEMENT_AU_PLUS_TARD BETWEEN ? AND ?") # Assuming load within this planned period
            params_list_of.extend([date_debut, date_fin])

        query_of_load = of_load_query_base
        if conditions_of:
            query_of_load += " AND " + " AND ".join(conditions_of)
        query_of_load += " GROUP BY COALESCE(OF_DA.FAMILLE_TECHNIQUE, 'GENERAL')"

        df_of_load = self.execute_query(query_of_load, tuple(params_list_of) if params_list_of else None)

        if df_personnel_charge is not None and df_of_load is not None:
            # Merge on SECTEUR (personnel) and SECTEUR_OF (OF load, aliased to SECTEUR for merge)
            df_of_load.rename(columns={'SECTEUR_OF': 'SECTEUR'}, inplace=True)
            merged_charge = pd.merge(df_personnel_charge, df_of_load, on='SECTEUR', how='left').fillna(0)
            merged_charge['TAUX_CHARGE_POURCENT'] = np.where(
                merged_charge['NB_HEURES_DISPONIBLES_SEMAINE'] > 0,
                (merged_charge['HEURES_PLANIFIEES_RESTANTES'] / merged_charge['NB_HEURES_DISPONIBLES_SEMAINE']) * 100,
                0
            )
            merged_charge.rename(columns={'HEURES_PLANIFIEES_RESTANTES': 'HEURES_PLANIFIEES'}, inplace=True)
            merged_charge.rename(columns={'NB_OF_EN_COURS_SECTEUR': 'NB_OF_EN_COURS'}, inplace=True)
            self._close_connection()
            return merged_charge
        elif df_personnel_charge is not None: # Only personnel data available
            df_personnel_charge['HEURES_PLANIFIEES'] = 0
            df_personnel_charge['NB_OF_EN_COURS'] = 0
            df_personnel_charge['TAUX_CHARGE_POURCENT'] = 0.0
            self._close_connection()
            return df_personnel_charge

        self._close_connection()
        return None


    def get_backlog_data(self, date_debut: Optional[str] = None,
                          date_fin: Optional[str] = None) -> Optional[pd.DataFrame]:
        query_base = """
        SELECT
            OF_DA.PRODUIT,
            OF_DA.DESIGNATION,
            OF_DA.NUMERO_OFDA,
            OF_DA.AFFAIRE AS NUMERO_COMMANDE,
            OF_DA.QUANTITE_DEMANDEE,
            OF_DA.CUMUL_ENTREES,
            (OF_DA.QUANTITE_DEMANDEE - COALESCE(OF_DA.CUMUL_ENTREES, 0)) AS QUANTITE_RESTANTE,
            OF_DA.LANCEMENT_AU_PLUS_TARD,
            OF_DA.STATUT,
            CASE
                WHEN OF_DA.LANCEMENT_AU_PLUS_TARD < TODAY() AND OF_DA.STATUT = 'C'
                THEN DATEDIFF(day, OF_DA.LANCEMENT_AU_PLUS_TARD, TODAY())
                ELSE 0
            END AS RETARD_JOURS,
            CASE
                WHEN OF_DA.LANCEMENT_AU_PLUS_TARD < TODAY() AND OF_DA.STATUT = 'C' THEN 'URGENT'
                WHEN DATEDIFF(day, TODAY(), OF_DA.LANCEMENT_AU_PLUS_TARD) <= 7 THEN 'PRIORITAIRE'
                ELSE 'NORMAL'
            END AS PRIORITE,
            CASE
                WHEN OF_DA.DUREE_PREVUE > OF_DA.CUMUL_TEMPS_PASSES
                THEN (OF_DA.DUREE_PREVUE - OF_DA.CUMUL_TEMPS_PASSES)
                ELSE 0.0
            END AS TEMPS_RESTANT_ESTIME,
            COALESCE(OF_DA.CLIENT, 'N/A') AS CLIENT
        FROM gpao.OF_DA OF_DA
        WHERE OF_DA.NUMERO_OFDA LIKE 'F%'
          AND OF_DA.STATUT IN ('C', 'A')
          AND (OF_DA.QUANTITE_DEMANDEE - COALESCE(OF_DA.CUMUL_ENTREES, 0)) > 0
        """
        conditions: List[str] = []
        params_list: List[Any] = []

        if date_debut and date_fin:
            conditions.append("OF_DA.LANCEMENT_AU_PLUS_TARD BETWEEN ? AND ?")
            params_list.extend([date_debut, date_fin])

        query = query_base
        if conditions:
            query += " AND " + " AND ".join(conditions)

        query += """
        ORDER BY
            CASE
                WHEN OF_DA.LANCEMENT_AU_PLUS_TARD < TODAY() AND OF_DA.STATUT = 'C' THEN 1
                WHEN DATEDIFF(day, TODAY(), OF_DA.LANCEMENT_AU_PLUS_TARD) <= 7 THEN 2
                ELSE 3
            END,
            OF_DA.LANCEMENT_AU_PLUS_TARD ASC
        """
        df = self.execute_query(query, tuple(params_list) if params_list else None)
        self._close_connection()
        return df

    def generate_summary_report(self, df: pd.DataFrame) -> str:
        if df is None or df.empty:
            return "Aucune donnée à afficher pour générer le rapport."

        report_lines = [
            f"=== RAPPORT DE SUIVI DE PRODUCTION EXCALIBUR ERP ===",
            f"Généré le: {pd.Timestamp.now().strftime('%d/%m/%Y à %H:%M')}\n",
            "=== STATISTIQUES GÉNÉRALES ==="
        ]

        total_of = len(df)
        report_lines.append(f"Nombre total d'OF analysés: {total_of}")
        for status, status_label in [('C', 'En cours'), ('T', 'Terminés'), ('A', 'Arrêtés')]:
            count = len(df[df['STATUT'] == status])
            report_lines.append(f"OF {status_label}: {count} ({count/total_of:.1%} du total)")

        report_lines.append("\n=== AVANCEMENTS MOYENS ===")
        avg_prod = df['Avancement_PROD'].mean() * 100
        avg_temps = df['Avancement_temps'].mean() * 100
        report_lines.append(f"Avancement moyen production: {avg_prod:.1f}%")
        report_lines.append(f"Avancement moyen temps: {avg_temps:.1f}%")

        if 'EFFICACITE' in df.columns:
            # Filter out potential inf or very large values if DUREE_PREVUE is high and CUMUL_TEMPS_PASSES is tiny
            valid_efficacite = df[ (df['EFFICACITE'] > 0) & (df['EFFICACITE'] < 5) ]['EFFICACITE'] # Cap at 500% for mean
            avg_efficacite = valid_efficacite.mean() if not valid_efficacite.empty else 0.0
            report_lines.append(f"Efficacité moyenne (0-5 cap): {avg_efficacite:.2f}")

        report_lines.append("\n=== ALERTES ET RETARDS ===")
        alertes_temps = len(df[df['Alerte_temps'] == True]) # Uses the boolean column
        taux_alerte = (alertes_temps / total_of * 100) if total_of > 0 else 0
        report_lines.append(f"Nombre d'OF avec dépassement temps: {alertes_temps}")
        report_lines.append(f"Taux d'alerte: {taux_alerte:.1f}%")

        # Analyse par FAMILLE_TECHNIQUE
        if 'FAMILLE_TECHNIQUE' in df.columns and not df['FAMILLE_TECHNIQUE'].empty:
            report_lines.append(f"\n=== ANALYSE PAR FAMILLE TECHNIQUE ===")
            famille_stats = df.groupby('FAMILLE_TECHNIQUE').agg(
                NB_OF=('NUMERO_OFDA', 'count'),
                AV_PROD_MOY=('Avancement_PROD', 'mean'),
                AV_TEMPS_MOY=('Avancement_temps', 'mean'),
                NB_ALERTES=('Alerte_temps', 'sum')
            ).reset_index()

            for _, row in famille_stats.iterrows():
                famille = row['FAMILLE_TECHNIQUE']
                if pd.notna(famille) and str(famille).strip() and str(famille).strip().upper() != 'INCONNUE':
                    report_lines.append(f"{famille}:")
                    report_lines.append(f"  - Nombre OF: {row['NB_OF']}")
                    report_lines.append(f"  - Avancement prod: {row['AV_PROD_MOY']*100:.1f}%")
                    report_lines.append(f"  - Avancement temps: {row['AV_TEMPS_MOY']*100:.1f}%")
                    report_lines.append(f"  - Alertes: {row['NB_ALERTES']}")
        else:
            report_lines.append("\n(Aucune donnée FAMILLE_TECHNIQUE pour analyse détaillée)")


        of_retard = df[df['Alerte_temps'] == True].sort_values('Avancement_temps', ascending=False)
        if not of_retard.empty:
            report_lines.append(f"\n=== TOP 5 OF EN RETARD (Dépassement Temps Prévu) ===")
            for i, (_, row) in enumerate(of_retard.head(5).iterrows()):
                retard_pct = (row['Avancement_temps'] - 1) * 100
                report_lines.append(f"{i+1}. OF: {row['NUMERO_OFDA']} ({row['PRODUIT']}) - Dépassement: {retard_pct:.1f}%")

        report_lines.append(f"\n=== RECOMMANDATIONS SOMMAIRES ===")
        if taux_alerte > 20:
            report_lines.append("⚠️ Taux d'alerte de dépassement temps élevé (>20%). Réviser la planification ou les estimations.")
        if 'avg_efficacite' in locals() and avg_efficacite < 0.8 and avg_efficacite > 0: # only if calculable
            report_lines.append("⚠️ Efficacité moyenne faible (<0.8). Analyser les causes de non-performance.")
        # Check for stopped OF
        of_arretes_count = len(df[df['STATUT'] == 'A'])
        if of_arretes_count > 0:
            report_lines.append(f"⚠️ {of_arretes_count} OF sont à l'état 'Arrêté'. Identifier et résoudre les blocages.")

        if taux_alerte <= 10 and ('avg_efficacite' not in locals() or avg_efficacite >= 0.95 or avg_efficacite == 0):
            report_lines.append("✅ Performance globale semble satisfaisante sur les OF analysés.")

        return "\n".join(report_lines)

    # Wrapper method to get all necessary data for a dashboard in one go
    def get_dashboard_data(self, date_debut: Optional[str] = None,
                            date_fin: Optional[str] = None,
                            statut_filter_of: Optional[str] = None) -> Dict[str, Optional[pd.DataFrame]]:
        """Récupère toutes les données nécessaires pour le tableau de bord."""
        # Note: Each call will establish/close its own connection unless execute_query is refactored
        # to keep connection open across a "session" managed by this method.
        # For now, simplicity over micro-optimization of connections.
        main_of_data = self.get_comprehensive_of_data(date_debut, date_fin, statut_filter_of)
        charge_data = self.get_charge_travail_data(date_debut, date_fin)
        backlog_data = self.get_backlog_data(date_debut, date_fin)
        personnel_data = self.get_personnel_data()

        return {
            'main_of_data': main_of_data,
            'charge_data': charge_data,
            'backlog_data': backlog_data,
            'personnel_data': personnel_data,
        }

if __name__ == "__main__":
    print("🚀 Module d'Analyse de Données Excalibur ERP")
    print("=" * 60)
    try:
        analyzer = ExcaliburDataAnalyzer()
        print("✅ Initialisation de l'analyseur réussie.")

        # Test de connexion (implicite dans la première requête)
        print("\n📊 Test: Récupération des données OF (10 premiers)...")
        # Example with date range
        date_debut_test = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        date_fin_test = datetime.now().strftime('%Y-%m-%d')

        df_test = analyzer.get_comprehensive_of_data(date_debut_test, date_fin_test, statut_filter="C")

        if df_test is not None and not df_test.empty:
            print(f"✅ Données OF récupérées: {len(df_test)} enregistrements.")
            print(df_test.head())

            print("\n📄 Test: Génération du rapport de synthèse...")
            report = analyzer.generate_summary_report(df_test)
            print(report)
        elif df_test is not None and df_test.empty:
            print("⚠️ Aucune donnée OF trouvée pour les critères de test.")
        else:
            print("❌ Échec de la récupération des données OF.")

        print("\n👥 Test: Données Personnel...")
        personnel_df = analyzer.get_personnel_data()
        if personnel_df is not None:
            print(f"✅ Données personnel récupérées: {len(personnel_df)} employés.")
        else:
            print("❌ Échec de la récupération des données personnel.")

        print("\n⚙️ Test: Données Charge Travail...")
        charge_df = analyzer.get_charge_travail_data(date_debut_test, date_fin_test)
        if charge_df is not None:
            print(f"✅ Données charge travail récupérées pour {len(charge_df)} secteurs.")
        else:
            print("❌ Échec de la récupération des données de charge.")

        print("\n📋 Test: Données Backlog...")
        backlog_df = analyzer.get_backlog_data(date_debut_test, date_fin_test)
        if backlog_df is not None:
            print(f"✅ Données backlog récupérées: {len(backlog_df)} OF en attente.")
        else:
            print("❌ Échec de la récupération des données de backlog.")

    except ValueError as ve:
        print(f"Erreur de configuration: {ve}")
    except Exception as e:
        print(f"Une erreur s'est produite lors des tests: {e}")

    print("\n✅ Tests du module d'analyse terminés.")