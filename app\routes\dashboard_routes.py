"""
Dashboard Routes - MVC Pattern Implementation
"""

from fastapi import APIRouter, Request, HTTPException, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from typing import Optional

from app.controllers.dashboard_controller import Dashboard<PERSON>ontroller
from app.models.schemas import BaseResponse

router = APIRouter(tags=["Dashboard"])
templates = Jinja2Templates(directory="templates")

# Initialize controller
dashboard_controller = DashboardController()


# View Routes (Templates)
@router.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Main dashboard page."""
    return templates.TemplateResponse("dashboard.html", {"request": request})


@router.get("/of", response_class=HTMLResponse)
async def of_management(request: Request):
    """OF Management page."""
    return templates.TemplateResponse("of_management.html", {"request": request})


# API Routes (Data)
@router.get("/api/dashboard-data", response_model=BaseResponse)
async def get_dashboard_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    famille_filter: Optional[str] = Query(None, description="Family filter"),
    client_filter: Optional[str] = Query(None, description="Client filter"),
    alerte_filter: Optional[bool] = Query(None, description="Alert filter")
):
    """Get all dashboard data including KPIs and detailed information."""
    try:
        data = dashboard_controller.get_dashboard_data(
            date_debut=date_debut,
            date_fin=date_fin,
            statut_filter=statut_filter,
            famille_filter=famille_filter,
            client_filter=client_filter,
            alerte_filter=alerte_filter
        )
        return BaseResponse(success=True, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching dashboard data: {str(e)}")


@router.get("/api/kpis", response_model=BaseResponse)
async def get_kpis():
    """Get KPIs only for dashboard widgets."""
    try:
        kpis = dashboard_controller.get_kpis()
        return BaseResponse(success=True, data={"kpis": kpis})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching KPIs: {str(e)}")


@router.get("/api/filters/options", response_model=BaseResponse)
async def get_filter_options():
    """Get available filter options for dropdowns."""
    try:
        options = dashboard_controller.get_filter_options()
        return BaseResponse(success=True, data={"options": options})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching filter options: {str(e)}")
