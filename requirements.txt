# FastAPI and web framework dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
jinja2>=3.1.0
python-multipart>=0.0.6

# Data processing dependencies
pandas>=2.0.0
numpy>=1.24.0
plotly>=5.15.0
pyodbc>=4.0.0
python-dotenv>=1.0.0

# Optional: Keep for potential data visualization
matplotlib>=3.7.0
seaborn>=0.12.0

# API documentation and validation
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Testing dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
httpx>=0.24.0
pytest-mock>=3.11.0
