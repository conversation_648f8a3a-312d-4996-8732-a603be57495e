<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Production Dashboard - Excalibur ERP</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>

    <style>
      :root {
        --primary-color: #2563eb;
        --secondary-color: #64748b;
        --success-color: #059669;
        --warning-color: #d97706;
        --danger-color: #dc2626;
        --light-bg: #f8fafc;
        --border-color: #e2e8f0;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
        background-color: var(--light-bg);
        color: var(--text-primary);
        line-height: 1.6;
      }

      .navbar {
        background: white;
        border-bottom: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        padding: 1rem 0;
      }

      .navbar-brand {
        font-weight: 600;
        color: var(--text-primary) !important;
        font-size: 1.25rem;
      }

      .sidebar {
        background: white;
        border-right: 1px solid var(--border-color);
        min-height: calc(100vh - 76px);
        padding: 1.5rem;
      }

      .sidebar-section {
        margin-bottom: 2rem;
      }

      .sidebar-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 1rem;
      }

      .nav-item {
        margin-bottom: 0.25rem;
      }

      .nav-link {
        color: var(--text-secondary);
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s;
        border: none;
        background: none;
      }

      .nav-link:hover {
        background-color: var(--light-bg);
        color: var(--text-primary);
      }

      .nav-link.active {
        background-color: var(--primary-color);
        color: white;
      }

      .main-content {
        padding: 2rem;
        background: var(--light-bg);
      }

      .metric-card {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        transition: all 0.2s;
      }

      .metric-card:hover {
        box-shadow: var(--shadow-md);
      }

      .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
      }

      .metric-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
      }

      .metric-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
      }

      .icon-primary {
        background-color: #dbeafe;
        color: var(--primary-color);
      }
      .icon-success {
        background-color: #d1fae5;
        color: var(--success-color);
      }
      .icon-warning {
        background-color: #fef3c7;
        color: var(--warning-color);
      }
      .icon-danger {
        background-color: #fee2e2;
        color: var(--danger-color);
      }

      .card {
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        box-shadow: var(--shadow-sm);
        background: white;
      }

      .card-header {
        background: white;
        border-bottom: 1px solid var(--border-color);
        padding: 1rem 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
      }

      .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
      }

      .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
        font-weight: 500;
      }

      .form-control,
      .form-select {
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
      }

      .form-control:focus,
      .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
      }

      .loading {
        display: none;
        text-align: center;
        padding: 3rem;
      }

      .table-container {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
      }

      .nav-tabs {
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 1.5rem;
      }

      .nav-tabs .nav-link {
        border: none;
        color: var(--text-secondary);
        font-weight: 500;
        padding: 0.75rem 1rem;
      }

      .nav-tabs .nav-link.active {
        background: none;
        color: var(--primary-color);
        border-bottom: 2px solid var(--primary-color);
      }

      .alert {
        border: none;
        border-radius: 0.5rem;
        padding: 1rem;
      }

      .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .status-connected {
        background-color: #d1fae5;
        color: var(--success-color);
      }

      .status-error {
        background-color: #fee2e2;
        color: var(--danger-color);
      }

      @media (max-width: 768px) {
        .sidebar {
          position: fixed;
          top: 76px;
          left: -100%;
          width: 280px;
          z-index: 1000;
          transition: left 0.3s;
        }

        .sidebar.show {
          left: 0;
        }

        .main-content {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg">
      <div class="container-fluid">
        <a class="navbar-brand" href="/">
          <i class="fas fa-industry me-2"></i>
          Excalibur ERP - Production Dashboard
        </a>

        <button
          class="navbar-toggler d-lg-none"
          type="button"
          onclick="toggleSidebar()"
        >
          <i class="fas fa-bars"></i>
        </button>

        <div class="navbar-nav ms-auto">
          <div class="nav-item">
            <span
              class="status-indicator status-connected"
              id="connectionStatus"
              style="display: none"
            >
              <i class="fas fa-circle"></i>
              Connected
            </span>
            <span
              class="status-indicator status-error"
              id="errorStatus"
              style="display: none"
            >
              <i class="fas fa-circle"></i>
              Error
            </span>
          </div>
        </div>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row">
        <!-- Simplified Sidebar -->
        <div class="col-lg-3 sidebar" id="sidebar">
          <!-- Navigation -->
          <div class="sidebar-section">
            <div class="sidebar-title">Navigation</div>
            <nav class="nav flex-column">
              <a href="/" class="nav-link active">
                <i class="fas fa-chart-line me-2"></i>
                Dashboard
              </a>
              <a href="/of" class="nav-link">
                <i class="fas fa-list-alt me-2"></i>
                Production Orders
              </a>
            </nav>
          </div>

          <!-- Quick Filters -->
          <div class="sidebar-section">
            <div class="sidebar-title">Filters</div>
            <div class="mb-3">
              <label for="dateDebut" class="form-label">Start Date</label>
              <input type="date" class="form-control" id="dateDebut" />
            </div>
            <div class="mb-3">
              <label for="dateFin" class="form-label">End Date</label>
              <input type="date" class="form-control" id="dateFin" />
            </div>
            <div class="mb-3">
              <label for="statutFilter" class="form-label">Status</label>
              <select class="form-select" id="statutFilter">
                <option value="">All</option>
                <option value="C">In Progress</option>
                <option value="T">Completed</option>
                <option value="A">Stopped</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="familleFilter" class="form-label">Family</label>
              <select class="form-select" id="familleFilter">
                <option value="">All</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="clientFilter" class="form-label">Client</label>
              <select class="form-select" id="clientFilter">
                <option value="">All</option>
              </select>
            </div>
            <div class="form-check mb-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="alerteFilter"
              />
              <label class="form-check-label" for="alerteFilter">
                Alerts only
              </label>
            </div>
            <button class="btn btn-primary w-100" onclick="refreshData()">
              <i class="fas fa-sync-alt me-2"></i>
              Refresh
            </button>
          </div>

          <!-- View Options -->
          <div class="sidebar-section">
            <div class="sidebar-title">View Options</div>
            <div class="form-check mb-2">
              <input
                class="form-check-input"
                type="checkbox"
                id="showOverview"
                checked
              />
              <label class="form-check-label" for="showOverview"
                >Overview</label
              >
            </div>
            <div class="form-check mb-2">
              <input
                class="form-check-input"
                type="checkbox"
                id="showDetails"
                checked
              />
              <label class="form-check-label" for="showDetails">Details</label>
            </div>
            <div class="form-check mb-2">
              <input
                class="form-check-input"
                type="checkbox"
                id="showCharts"
                checked
              />
              <label class="form-check-label" for="showCharts">Charts</label>
            </div>
            <div class="form-check mb-2">
              <input class="form-check-input" type="checkbox" id="showReport" />
              <label class="form-check-label" for="showReport">Report</label>
            </div>
            <div class="form-check mb-2">
              <input class="form-check-input" type="checkbox" id="showCharge" />
              <label class="form-check-label" for="showCharge">Workload</label>
            </div>
            <div class="form-check mb-2">
              <input
                class="form-check-input"
                type="checkbox"
                id="showBacklog"
              />
              <label class="form-check-label" for="showBacklog">Backlog</label>
            </div>
            <div class="form-check mb-2">
              <input
                class="form-check-input"
                type="checkbox"
                id="showPersonnel"
              />
              <label class="form-check-label" for="showPersonnel"
                >Personnel</label
              >
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="sidebar-section">
            <div class="sidebar-title">Export</div>
            <div class="d-grid gap-2">
              <button
                class="btn btn-outline-primary btn-sm"
                onclick="exportData('csv')"
              >
                <i class="fas fa-download me-2"></i>CSV
              </button>
              <button
                class="btn btn-outline-primary btn-sm"
                onclick="exportData('excel')"
              >
                <i class="fas fa-download me-2"></i>Excel
              </button>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9 main-content">
          <!-- Page Header -->
          <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 class="h3 mb-1">Production Dashboard</h1>
              <p class="text-muted mb-0">
                Real-time production monitoring and analytics
              </p>
            </div>
            <div class="d-flex gap-2">
              <button class="btn btn-outline-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt me-1"></i>
                Refresh
              </button>
              <div class="dropdown">
                <button
                  class="btn btn-outline-secondary dropdown-toggle"
                  type="button"
                  data-bs-toggle="dropdown"
                >
                  <i class="fas fa-download me-1"></i>
                  Export
                </button>
                <ul class="dropdown-menu">
                  <li>
                    <a
                      class="dropdown-item"
                      href="#"
                      onclick="exportData('csv')"
                      >Export as CSV</a
                    >
                  </li>
                  <li>
                    <a
                      class="dropdown-item"
                      href="#"
                      onclick="exportData('excel')"
                      >Export as Excel</a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Loading indicator -->
          <div class="loading" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading data...</p>
          </div>

          <!-- Alerts -->
          <div
            class="alert alert-success"
            id="connectionAlert"
            style="display: none"
          >
            <i class="fas fa-check-circle me-2"></i>
            <span id="connectionMessage">Connected to Excalibur ERP</span>
            <span id="lastUpdateTime" class="float-end"></span>
          </div>

          <div class="alert alert-danger" id="errorAlert" style="display: none">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span id="errorMessage"></span>
          </div>

          <div class="alert alert-info" id="infoAlert" style="display: none">
            <i class="fas fa-info-circle me-2"></i>
            <span id="infoMessage"></span>
          </div>

          <!-- KPI Cards -->
          <div class="row g-3 mb-4" id="kpiCards">
            <!-- KPI cards will be populated by JavaScript -->
          </div>

          <!-- Main Content Tabs -->
          <div class="card">
            <div class="card-header">
              <ul
                class="nav nav-tabs card-header-tabs"
                id="mainTabs"
                role="tablist"
              >
                <!-- Tabs will be populated by JavaScript -->
              </ul>
            </div>
            <div class="card-body">
              <div class="tab-content" id="mainTabContent">
                <!-- Tab content will be populated by JavaScript -->
              </div>
            </div>
          </div>

          <!-- Data Summary -->
          <div class="row mt-4">
            <div class="col-12">
              <div class="card">
                <div class="card-body">
                  <div class="row text-center">
                    <div class="col-md-3">
                      <div class="text-muted small">Total Records</div>
                      <div class="h5 mb-0" id="dataCount">-</div>
                    </div>
                    <div class="col-md-3">
                      <div class="text-muted small">Last Updated</div>
                      <div class="h6 mb-0" id="lastRefresh">-</div>
                    </div>
                    <div class="col-md-3">
                      <div class="text-muted small">Status</div>
                      <div class="h6 mb-0" id="systemStatus">-</div>
                    </div>
                    <div class="col-md-3">
                      <div class="text-muted small">Response Time</div>
                      <div class="h6 mb-0" id="responseTime">-</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
      // Mobile sidebar toggle
      function toggleSidebar() {
        const sidebar = document.getElementById("sidebar");
        sidebar.classList.toggle("show");
      }

      // Close sidebar when clicking outside on mobile
      document.addEventListener("click", function (event) {
        const sidebar = document.getElementById("sidebar");
        const toggleButton = document.querySelector(".navbar-toggler");

        if (
          window.innerWidth <= 768 &&
          !sidebar.contains(event.target) &&
          !toggleButton.contains(event.target) &&
          sidebar.classList.contains("show")
        ) {
          sidebar.classList.remove("show");
        }
      });
    </script>
    <script src="/static/dashboard.js"></script>
  </body>
</html>
