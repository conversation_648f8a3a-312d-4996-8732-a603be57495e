"""
Configuration settings for the FastAPI application.
"""

import os
from functools import lru_cache
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    app_name: str = "Suivi Production - Excalibur ERP"
    app_description: str = "API pour le suivi en temps réel des indicateurs de production"
    app_version: str = "2.0.0"
    debug: bool = False
    
    # Database settings
    db_uid: Optional[str] = None
    db_pwd: Optional[str] = None
    db_host: Optional[str] = None
    db_server_name: Optional[str] = None
    db_database_name: Optional[str] = None
    
    # Server settings
    host: str = "localhost"
    port: int = 8000
    reload: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
