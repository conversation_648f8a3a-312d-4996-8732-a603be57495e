#!/usr/bin/env python3
"""
Application Health Monitor for Suivi de Production
Monitors the FastAPI application and database connectivity.
"""

import requests
import time
import json
from datetime import datetime
import sys
import argparse


class ApplicationMonitor:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.endpoints = {
            'health': '/api/health/',
            'database': '/api/health/database',
            'performance': '/api/health/performance',
            'dashboard': '/',
            'of_management': '/of'
        }
    
    def check_endpoint(self, name, path, timeout=10):
        """Check a single endpoint and return status info."""
        url = f"{self.base_url}{path}"
        start_time = time.time()
        
        try:
            response = requests.get(url, timeout=timeout)
            response_time = (time.time() - start_time) * 1000  # Convert to ms
            
            status = {
                'name': name,
                'url': url,
                'status_code': response.status_code,
                'response_time_ms': round(response_time, 2),
                'success': response.status_code == 200,
                'timestamp': datetime.now().isoformat()
            }
            
            # Try to parse JSON response for API endpoints
            if path.startswith('/api/'):
                try:
                    data = response.json()
                    status['api_success'] = data.get('success', False)
                    status['message'] = data.get('message', '')
                except:
                    status['api_success'] = False
                    status['message'] = 'Invalid JSON response'
            
            return status
            
        except requests.exceptions.ConnectionError:
            return {
                'name': name,
                'url': url,
                'status_code': 0,
                'response_time_ms': 0,
                'success': False,
                'error': 'Connection refused - server may be down',
                'timestamp': datetime.now().isoformat()
            }
        except requests.exceptions.Timeout:
            return {
                'name': name,
                'url': url,
                'status_code': 0,
                'response_time_ms': timeout * 1000,
                'success': False,
                'error': f'Timeout after {timeout}s',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'name': name,
                'url': url,
                'status_code': 0,
                'response_time_ms': 0,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def run_health_check(self):
        """Run a complete health check of all endpoints."""
        print(f"🔍 Health Check - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        results = {}
        all_healthy = True
        
        for name, path in self.endpoints.items():
            result = self.check_endpoint(name, path)
            results[name] = result
            
            # Print status
            status_icon = "✅" if result['success'] else "❌"
            print(f"{status_icon} {name.upper():<12} | {result['response_time_ms']:>6.1f}ms | {result.get('status_code', 'N/A')}")
            
            if result.get('error'):
                print(f"   Error: {result['error']}")
            elif result.get('message'):
                print(f"   Message: {result['message']}")
            
            if not result['success']:
                all_healthy = False
        
        print("=" * 60)
        overall_status = "✅ ALL SYSTEMS HEALTHY" if all_healthy else "⚠️  ISSUES DETECTED"
        print(f"Overall Status: {overall_status}")
        
        return results, all_healthy
    
    def monitor_continuous(self, interval=60):
        """Run continuous monitoring with specified interval."""
        print(f"🔄 Starting continuous monitoring (interval: {interval}s)")
        print("Press Ctrl+C to stop")
        print()
        
        try:
            while True:
                results, healthy = self.run_health_check()
                
                if not healthy:
                    # Log to file or send alert here
                    self.log_issue(results)
                
                print(f"\n⏰ Next check in {interval} seconds...\n")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped by user")
    
    def log_issue(self, results):
        """Log issues to a file for later analysis."""
        timestamp = datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'results': results
        }
        
        try:
            with open('app_health.log', 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
        except Exception as e:
            print(f"Failed to write log: {e}")


def main():
    parser = argparse.ArgumentParser(description='Monitor Suivi de Production application')
    parser.add_argument('--url', default='http://localhost:8000', 
                       help='Base URL of the application (default: http://localhost:8000)')
    parser.add_argument('--continuous', action='store_true',
                       help='Run continuous monitoring')
    parser.add_argument('--interval', type=int, default=60,
                       help='Monitoring interval in seconds (default: 60)')
    
    args = parser.parse_args()
    
    monitor = ApplicationMonitor(args.url)
    
    if args.continuous:
        monitor.monitor_continuous(args.interval)
    else:
        results, healthy = monitor.run_health_check()
        sys.exit(0 if healthy else 1)


if __name__ == "__main__":
    main()
