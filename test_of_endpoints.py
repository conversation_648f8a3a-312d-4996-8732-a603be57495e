#!/usr/bin/env python3
"""
Test script to verify OF endpoints are working correctly
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8001"

def test_endpoint(endpoint, description):
    """Test a single endpoint and return results"""
    print(f"\n🔍 Testing {description}")
    print(f"   URL: {BASE_URL}{endpoint}")
    
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get("success"):
                    if "data" in data:
                        if "of_list" in data["data"]:
                            count = len(data["data"]["of_list"])
                            print(f"   ✅ Success: {count} records found")
                            if count > 0:
                                first_record = data["data"]["of_list"][0]
                                print(f"   📋 Sample fields: {list(first_record.keys())[:5]}...")
                            return True
                        else:
                            print(f"   ✅ Success: {data}")
                            return True
                    else:
                        print(f"   ✅ Success: {data}")
                        return True
                else:
                    print(f"   ❌ API returned success=false: {data.get('message', 'No message')}")
                    return False
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON response")
                print(f"   Response: {response.text[:200]}...")
                return False
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Connection Error: Server not running?")
        return False
    except requests.exceptions.Timeout:
        print(f"   ❌ Timeout: Request took too long")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing OF API Endpoints")
    print("=" * 50)
    
    # Test endpoints
    endpoints = [
        ("/api/health/", "Health Check"),
        ("/api/of/en_cours", "OF En Cours"),
        ("/api/of/histo", "OF Historique"),
        ("/api/of/all", "All OF Data"),
        ("/api/of/by_status/C", "OF By Status (C)"),
    ]
    
    results = []
    for endpoint, description in endpoints:
        success = test_endpoint(endpoint, description)
        results.append((description, success))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} - {description}")
        if success:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The OF endpoints are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the server logs for more details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
