#!/usr/bin/env python3
"""
Configuration Backup Script for Suivi de Production
Creates backups of important configuration and application files.
"""

import os
import shutil
import zipfile
from datetime import datetime
import argparse
import json


class ConfigBackup:
    def __init__(self, backup_dir="backups"):
        self.backup_dir = backup_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Files and directories to backup
        self.backup_items = {
            'config_files': [
                'main.py',
                'run_fastapi.py',
                'requirements.txt',
                '.env.template',
                'docker-compose.yml',
                'Dockerfile'
            ],
            'documentation': [
                'README.md',
                'DOCUMENTATION_TECHNIQUE.md',
                'API_ROUTES_DOCUMENTATION.md',
                'DEPLOYMENT_CHECKLIST.md',
                'Cahier des Charges.html'
            ],
            'application_code': [
                'app/',
                'static/',
                'templates/'
            ],
            'diagrams': [
                'diags/'
            ],
            'scripts': [
                'monitor_app.py',
                'backup_config.py',
                'analyse_donnees.py',
                'app_suivi_production.py'
            ]
        }
    
    def create_backup_dir(self):
        """Create backup directory if it doesn't exist."""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            print(f"📁 Created backup directory: {self.backup_dir}")
    
    def backup_files(self, category, files):
        """Backup a category of files."""
        backup_path = os.path.join(self.backup_dir, f"{category}_{self.timestamp}")
        
        if not os.path.exists(backup_path):
            os.makedirs(backup_path)
        
        backed_up = []
        skipped = []
        
        for item in files:
            if os.path.exists(item):
                if os.path.isfile(item):
                    shutil.copy2(item, backup_path)
                    backed_up.append(item)
                elif os.path.isdir(item):
                    dest_dir = os.path.join(backup_path, os.path.basename(item))
                    if not os.path.exists(dest_dir):
                        shutil.copytree(item, dest_dir)
                        backed_up.append(item)
                    else:
                        skipped.append(f"{item} (already exists)")
            else:
                skipped.append(item)
        
        return backed_up, skipped, backup_path
    
    def create_full_backup(self):
        """Create a complete backup of all important files."""
        print(f"🔄 Starting full backup - {self.timestamp}")
        print("=" * 50)
        
        self.create_backup_dir()
        
        # Create a zip file for the complete backup
        zip_filename = os.path.join(self.backup_dir, f"suivi_production_backup_{self.timestamp}.zip")
        
        backup_info = {
            'timestamp': self.timestamp,
            'backup_date': datetime.now().isoformat(),
            'categories': {}
        }
        
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            total_backed_up = 0
            total_skipped = 0
            
            for category, files in self.backup_items.items():
                print(f"\n📦 Backing up {category}...")
                
                backed_up, skipped, _ = self.backup_files(category, files)
                
                # Add files to zip
                for item in backed_up:
                    if os.path.isfile(item):
                        zipf.write(item, f"{category}/{item}")
                    elif os.path.isdir(item):
                        for root, dirs, files_in_dir in os.walk(item):
                            for file in files_in_dir:
                                file_path = os.path.join(root, file)
                                arc_path = f"{category}/{file_path}"
                                zipf.write(file_path, arc_path)
                
                # Update backup info
                backup_info['categories'][category] = {
                    'backed_up': backed_up,
                    'skipped': skipped,
                    'count_backed_up': len(backed_up),
                    'count_skipped': len(skipped)
                }
                
                total_backed_up += len(backed_up)
                total_skipped += len(skipped)
                
                print(f"   ✅ {len(backed_up)} items backed up")
                if skipped:
                    print(f"   ⚠️  {len(skipped)} items skipped: {', '.join(skipped)}")
            
            # Add backup info to zip
            info_json = json.dumps(backup_info, indent=2)
            zipf.writestr("backup_info.json", info_json)
        
        # Get zip file size
        zip_size = os.path.getsize(zip_filename)
        zip_size_mb = zip_size / (1024 * 1024)
        
        print("\n" + "=" * 50)
        print(f"✅ Backup completed successfully!")
        print(f"📁 Backup file: {zip_filename}")
        print(f"📊 Total items backed up: {total_backed_up}")
        print(f"⚠️  Total items skipped: {total_skipped}")
        print(f"💾 Backup size: {zip_size_mb:.2f} MB")
        
        return zip_filename, backup_info
    
    def list_backups(self):
        """List all available backups."""
        if not os.path.exists(self.backup_dir):
            print("📁 No backup directory found")
            return []
        
        backups = []
        for file in os.listdir(self.backup_dir):
            if file.endswith('.zip') and 'suivi_production_backup_' in file:
                file_path = os.path.join(self.backup_dir, file)
                size = os.path.getsize(file_path)
                mtime = os.path.getmtime(file_path)
                backups.append({
                    'filename': file,
                    'path': file_path,
                    'size_mb': size / (1024 * 1024),
                    'date': datetime.fromtimestamp(mtime)
                })
        
        backups.sort(key=lambda x: x['date'], reverse=True)
        
        print("📋 Available backups:")
        print("-" * 50)
        for backup in backups:
            print(f"📦 {backup['filename']}")
            print(f"   📅 Date: {backup['date'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   💾 Size: {backup['size_mb']:.2f} MB")
            print()
        
        return backups
    
    def cleanup_old_backups(self, keep_count=5):
        """Remove old backups, keeping only the most recent ones."""
        backups = self.list_backups()
        
        if len(backups) <= keep_count:
            print(f"📁 Only {len(backups)} backups found, no cleanup needed")
            return
        
        to_remove = backups[keep_count:]
        
        print(f"🧹 Cleaning up {len(to_remove)} old backups (keeping {keep_count} most recent)")
        
        for backup in to_remove:
            try:
                os.remove(backup['path'])
                print(f"   🗑️  Removed: {backup['filename']}")
            except Exception as e:
                print(f"   ❌ Failed to remove {backup['filename']}: {e}")


def main():
    parser = argparse.ArgumentParser(description='Backup Suivi de Production configuration')
    parser.add_argument('--backup-dir', default='backups',
                       help='Backup directory (default: backups)')
    parser.add_argument('--list', action='store_true',
                       help='List available backups')
    parser.add_argument('--cleanup', type=int, metavar='N',
                       help='Keep only N most recent backups')
    
    args = parser.parse_args()
    
    backup = ConfigBackup(args.backup_dir)
    
    if args.list:
        backup.list_backups()
    elif args.cleanup:
        backup.cleanup_old_backups(args.cleanup)
    else:
        backup.create_full_backup()


if __name__ == "__main__":
    main()
