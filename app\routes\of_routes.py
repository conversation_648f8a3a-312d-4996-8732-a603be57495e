"""
OF Routes - MVC Pattern Implementation
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional

from app.controllers.of_controller import OFController
from app.models.schemas import BaseResponse

router = APIRouter(prefix="/api/of", tags=["Orders of Fabrication"])

# Initialize controller
of_controller = OFController()


@router.get("/en_cours", response_model=BaseResponse)
async def get_of_en_cours():
    """Get OF currently in progress."""
    try:
        data = of_controller.get_of_en_cours()
        return BaseResponse(success=True, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching OF en cours: {str(e)}")


@router.get("/histo", response_model=BaseResponse)
async def get_of_histo():
    """Get historical OF data (completed and stopped)."""
    try:
        data = of_controller.get_of_histo()
        return BaseResponse(success=True, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching OF historique: {str(e)}")


@router.get("/all", response_model=BaseResponse)
async def get_all_of(limit: Optional[int] = Query(None, description="Limit number of results")):
    """Get all OF data."""
    try:
        data = of_controller.get_all_of(limit=limit)
        return BaseResponse(success=True, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching all OF: {str(e)}")


@router.get("/filtered", response_model=BaseResponse)
async def get_filtered_of_data(
    date_debut: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    date_fin: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    statut_filter: Optional[str] = Query(None, description="Status filter (C/T/A)"),
    famille_filter: Optional[str] = Query(None, description="Family filter"),
    client_filter: Optional[str] = Query(None, description="Client filter"),
    alerte_filter: Optional[bool] = Query(None, description="Alert filter"),
    limit: Optional[int] = Query(None, description="Limit number of results")
):
    """Get filtered OF data with comprehensive filters."""
    try:
        data = of_controller.get_of_with_filters(
            date_debut=date_debut,
            date_fin=date_fin,
            statut_filter=statut_filter,
            famille_filter=famille_filter,
            client_filter=client_filter,
            alerte_filter=alerte_filter,
            limit=limit
        )
        return BaseResponse(success=True, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching filtered OF data: {str(e)}")


@router.get("/by_status/{status}", response_model=BaseResponse)
async def get_of_by_status(status: str):
    """Get OF data by specific status."""
    try:
        data = of_controller.get_of_by_status(status)
        return BaseResponse(success=True, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching OF by status {status}: {str(e)}")


@router.get("/statistics", response_model=BaseResponse)
async def get_of_statistics():
    """Get OF statistics summary."""
    try:
        data = of_controller.get_of_statistics()
        return BaseResponse(success=True, data=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching OF statistics: {str(e)}")
