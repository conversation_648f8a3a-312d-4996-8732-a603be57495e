#!/usr/bin/env python3
"""
Application Streamlit de suivi du temps de production - Excalibur ERP
Version professionnelle améliorée.
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
import plotly.express as px
import plotly.graph_objects as go
from analyse_donnees import ExcaliburDataAnalyzer # Make sure this is importable
from typing import Optional, Dict, Any
import io # For Excel export

# --- Page Configuration ---
st.set_page_config(
    page_title="Suivi Production - Excalibur",
    page_icon="🏭", # Changed icon
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'mailto:<EMAIL>', # Replace with actual help
        'Report a bug': "mailto:<EMAIL>", # Replace
        'About': """
        ## Suivi de Production - Excalibur ERP
        Application pour le suivi en temps réel des indicateurs de production.
        Version 1.0. Développée pour EmkaMed.
        """
    }
)

# --- Global Styles (Optional) ---
# st.markdown("""
# <style>
# /* Add custom CSS here */
# .stMetric {
#     border: 1px solid #EEEEEE;
#     border-radius: 10px;
#     padding: 10px;
# }
# </style>
# """, unsafe_allow_html=True)


# --- Caching ---
@st.cache_resource # Cache the analyzer instance for the session
def init_analyzer() -> Optional[ExcaliburDataAnalyzer]:
    try:
        analyzer = ExcaliburDataAnalyzer()
        # Perform a lightweight test connection (e.g., select current date from DB)
        # For now, we assume __init__ or first call will reveal connection issues
        return analyzer
    except ValueError as ve: # Catches .env config errors
        st.error(f"Erreur de configuration de la base de données: {ve}")
        return None
    except Exception as e:
        st.error(f"Erreur inattendue lors de l'initialisation de l'analyseur: {e}")
        return None

@st.cache_data(ttl=300) # Cache data for 5 minutes
def load_dashboard_data(_analyzer_ref: int, # Used to bust cache if analyzer changes (not expected here)
                        date_debut_str: str, date_fin_str: str,
                        statut_filter_of: Optional[str]) -> Dict[str, Optional[pd.DataFrame]]:
    analyzer = init_analyzer() # Gets cached analyzer
    if analyzer:
        return analyzer.get_dashboard_data(date_debut_str, date_fin_str, statut_filter_of)
    return {key: None for key in ['main_of_data', 'charge_data', 'backlog_data', 'personnel_data']}


# --- UI Helper Functions ---
def display_kpi_metric(label: str, value: Any, help_text: Optional[str] = None,
                       delta: Optional[str] = None, delta_color: str = "normal"):
    """Helper to display styled metrics."""
    st.metric(label, value, delta=delta, delta_color=delta_color, help=help_text)

def dataframe_to_excel(df_dict: Dict[str, pd.DataFrame]) -> bytes:
    """Exports a dictionary of dataframes to an Excel file in memory."""
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        for sheet_name, df_data in df_dict.items():
            if df_data is not None and not df_data.empty:
                # Remove timezone if present for Excel compatibility
                for col in df_data.select_dtypes(['datetimetz']).columns:
                    df_data[col] = df_data[col].dt.tz_localize(None)
                df_data.to_excel(writer, sheet_name=sheet_name, index=False)
    return output.getvalue()

# --- Main Application Logic ---
def main():
    st.title("🏭 Suivi de Production - Excalibur ERP")
    st.markdown("*Tableau de bord pour l'analyse des performances et de la charge de travail*")
    st.markdown("---")

    analyzer = init_analyzer()
    if analyzer is None:
        st.warning("🚫 L'application ne peut pas fonctionner sans connexion à la base de données.")
        st.info("Veuillez vérifier la configuration (.env) et l'accessibilité du serveur Excalibur.")
        return

    # --- Sidebar Controls ---
    with st.sidebar:
        st.header("⚙️ Filtres & Options")
        st.success("🟢 Connecté à Excalibur ERP") # Assuming init_analyzer success means connection is possible

        st.subheader("📅 Période d'Analyse")
        today = date.today()
        default_start_date = today - timedelta(days=90)
        date_debut = st.date_input("Date de début", value=default_start_date, max_value=today)
        date_fin = st.date_input("Date de fin", value=today, min_value=date_debut, max_value=today)

        st.subheader("📋 Filtre Statut OF")
        statut_options = {"Tous": None, "En Cours (C)": "C", "Terminés (T)": "T", "Arrêtés (A)": "A"}
        selected_statut_label = st.selectbox("Statut des OF", options=list(statut_options.keys()), index=0)
        statut_param = statut_options[selected_statut_label]

        st.subheader("👁️ Affichage des Sections")
        col1_opts, col2_opts = st.columns(2)
        with col1_opts:
            show_dashboard_overview = st.checkbox("Synthèse Dashboard", value=True)
            show_of_details = st.checkbox("Détail OF", value=True)
            show_charts = st.checkbox("Graphiques Production", value=True)
        with col2_opts:
            show_report = st.checkbox("Rapport Textuel", value=False)
            show_charge = st.checkbox("Charge Travail", value=False)
            show_backlog = st.checkbox("Backlog", value=False)
            show_personnel = st.checkbox("Personnel", value=False)

        st.markdown("---")
        if st.button("🔄 Actualiser les Données", type="primary", use_container_width=True):
            # Bust cache for load_dashboard_data by changing one of its args slightly if needed,
            # or use st.cache_data.clear() selectively.
            # For simplicity, re-running with same params will use cache unless TTL expires.
            st.rerun()

        st.markdown("---")
        st.info("""
        **Aide Rapide:**
        - **Avancement Prod**: % Quantité produite / demandée.
        - **Avancement Temps**: % Temps passé / prévu.
        - **⚠️ Alerte**: OF dont le temps passé > temps prévu.
        - **Efficacité**: Ratio Temps Prévu / Temps Passé.
        """)

    # --- Data Loading ---
    # Use id(analyzer) to ensure cache is tied to this specific analyzer instance,
    # though with @st.cache_resource, analyzer itself is cached.
    dashboard_data_dict = load_dashboard_data(id(analyzer),
                                          date_debut.strftime('%Y-%m-%d'),
                                          date_fin.strftime('%Y-%m-%d'),
                                          statut_param)

    main_of_data = dashboard_data_dict.get('main_of_data')
    charge_data = dashboard_data_dict.get('charge_data')
    backlog_data = dashboard_data_dict.get('backlog_data')
    personnel_data = dashboard_data_dict.get('personnel_data')

    if main_of_data is None : # or main_of_data.empty: # Could be empty on valid filters
        st.warning("⚠️ Aucune donnée d'Ordre de Fabrication trouvée pour les critères sélectionnés. "
                   "Veuillez ajuster les filtres ou vérifier la base de données.")
        # Optionally allow proceeding if other data (personnel, etc.) is available and useful standalone
        # return # If main_of_data is critical for all views

    # --- Main Content Area with Tabs ---
    active_tabs = []
    if show_dashboard_overview: active_tabs.append("📊 Synthèse")
    if show_of_details and main_of_data is not None: active_tabs.append("📋 Détail OF")
    if show_charts and main_of_data is not None: active_tabs.append("📈 Graphiques Prod.")
    if show_report and main_of_data is not None: active_tabs.append("📄 Rapport")
    if show_charge and charge_data is not None: active_tabs.append("⚙️ Charge Travail")
    if show_backlog and backlog_data is not None: active_tabs.append("⏳ Backlog")
    if show_personnel and personnel_data is not None: active_tabs.append("👥 Personnel")

    if not active_tabs:
        st.info("ℹ️ Veuillez sélectionner au moins une section à afficher dans la barre latérale.")
        return

    tab_views = st.tabs(active_tabs)
    current_tab_idx = 0

    def get_next_tab():
        nonlocal current_tab_idx
        if current_tab_idx < len(tab_views):
            tab = tab_views[current_tab_idx]
            current_tab_idx += 1
            return tab
        return None # Should not happen if logic is correct

    if show_dashboard_overview:
        with get_next_tab():
            render_dashboard_overview_tab(main_of_data, backlog_data, charge_data)
    if show_of_details and main_of_data is not None:
        with get_next_tab():
            render_of_details_tab(main_of_data)
    if show_charts and main_of_data is not None:
        with get_next_tab():
            render_charts_tab(main_of_data)
    if show_report and main_of_data is not None:
        with get_next_tab():
            render_report_tab(main_of_data, analyzer) # Pass analyzer for report generation
    if show_charge and charge_data is not None:
        with get_next_tab():
            render_charge_tab(charge_data)
    if show_backlog and backlog_data is not None:
        with get_next_tab():
            render_backlog_tab(backlog_data)
    if show_personnel and personnel_data is not None:
        with get_next_tab():
            render_personnel_tab(personnel_data)

    # --- Export All Data Button ---
    st.sidebar.markdown("---")
    st.sidebar.subheader("📤 Export Données")
    export_filename = f"export_production_excalibur_{date_debut}_a_{date_fin}.xlsx"
    excel_data = dataframe_to_excel({
        "OF_Principaux": main_of_data,
        "Charge_Travail": charge_data,
        "Backlog": backlog_data,
        "Personnel": personnel_data
    })
    st.sidebar.download_button(
        label="Télécharger tout en Excel",
        data=excel_data,
        file_name=export_filename,
        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        use_container_width=True
    )

# --- Tab Rendering Functions ---
def render_dashboard_overview_tab(of_data: Optional[pd.DataFrame],
                                 backlog_df: Optional[pd.DataFrame],
                                 charge_df: Optional[pd.DataFrame]):
    st.header("📊 Synthèse Globale de la Production")

    if of_data is None or of_data.empty:
        st.info("Pas de données OF pour la synthèse.")
        return

    cols_kpi1 = st.columns(4)
    with cols_kpi1[0]: display_kpi_metric("Total OF Analysés", len(of_data))
    with cols_kpi1[1]: display_kpi_metric("OF en Cours", len(of_data[of_data['STATUT'] == 'C']))
    with cols_kpi1[2]: display_kpi_metric("OF Terminés", len(of_data[of_data['STATUT'] == 'T']))
    with cols_kpi1[3]: display_kpi_metric("OF Arrêtés", len(of_data[of_data['STATUT'] == 'A']))

    cols_kpi2 = st.columns(4)
    try:
        # Convert to numeric if needed and handle potential string values
        if not of_data.empty and 'Avancement_PROD' in of_data.columns:
            of_data['Avancement_PROD'] = pd.to_numeric(of_data['Avancement_PROD'], errors='coerce')
            avg_prod_kpi = of_data['Avancement_PROD'].mean() * 100
        else:
            avg_prod_kpi = 0

        if not of_data.empty and 'Avancement_temps' in of_data.columns:
            of_data['Avancement_temps'] = pd.to_numeric(of_data['Avancement_temps'], errors='coerce')
            avg_temps_kpi = of_data['Avancement_temps'].mean() * 100
        else:
            avg_temps_kpi = 0

        alertes_kpi = len(of_data[of_data['Alerte_temps'] == True]) if not of_data.empty and 'Alerte_temps' in of_data.columns else 0

        if not of_data.empty and 'EFFICACITE' in of_data.columns:
            of_data['EFFICACITE'] = pd.to_numeric(of_data['EFFICACITE'], errors='coerce')
            efficacite_kpi = of_data[ (of_data['EFFICACITE'] > 0) & (of_data['EFFICACITE'] < 5) ]['EFFICACITE'].mean()
        else:
            efficacite_kpi = 0
    except Exception as e:
        st.error(f"Erreur lors du calcul des KPI: {e}")
        avg_prod_kpi = avg_temps_kpi = alertes_kpi = efficacite_kpi = 0

    with cols_kpi2[0]: display_kpi_metric("Avanc. Prod. Moyen", f"{avg_prod_kpi:.1f}%")
    with cols_kpi2[1]: display_kpi_metric("Avanc. Temps Moyen", f"{avg_temps_kpi:.1f}%")
    with cols_kpi2[2]: display_kpi_metric("⚠️ OF en Alerte Temps", alertes_kpi, delta_color="inverse" if alertes_kpi > (0.1 * len(of_data)) else "normal")
    with cols_kpi2[3]: display_kpi_metric("Efficacité Moyenne", f"{efficacite_kpi:.2f}")

    st.markdown("---")
    cols_charts = st.columns(2)
    with cols_charts[0]:
        st.subheader("Répartition des Statuts OF")
        if not of_data.empty:
            statut_counts = of_data['STATUT'].value_counts()
            fig_pie = px.pie(statut_counts, values=statut_counts.values, names=statut_counts.index,
                             title="Statuts OF", hole=0.3, template="seaborn")
            fig_pie.update_traces(textposition='inside', textinfo='percent+label')
            st.plotly_chart(fig_pie, use_container_width=True)

    with cols_charts[1]:
        st.subheader("OF en Alerte vs Normaux")
        if not of_data.empty:
            alert_counts = of_data['Alerte_temps'].value_counts()
            alert_labels = {True: 'En Alerte', False: 'Normal'}
            fig_alerts = px.bar(x=[alert_labels.get(idx, 'N/A') for idx in alert_counts.index],
                                y=alert_counts.values, title="Alertes Temps vs Normaux",
                                color=[alert_labels.get(idx, 'N/A') for idx in alert_counts.index],
                                color_discrete_map={'En Alerte': 'orangered', 'Normal': 'mediumseagreen'},
                                template="seaborn", labels={'x': 'Statut Alerte', 'y': 'Nombre OF'})
            st.plotly_chart(fig_alerts, use_container_width=True)

    # Quick Backlog & Charge Info
    if backlog_df is not None and not backlog_df.empty:
        st.markdown("---")
        st.subheader("Aperçu Backlog Rapide")
        b_cols = st.columns(3)
        with b_cols[0]: display_kpi_metric("OF en Backlog", len(backlog_df))
        with b_cols[1]: display_kpi_metric("OF Urgents (Backlog)", len(backlog_df[backlog_df['PRIORITE'] == 'URGENT']))
        with b_cols[2]: display_kpi_metric("Qté Restante (Backlog)", f"{backlog_df['QUANTITE_RESTANTE'].sum():.0f} unités")

    if charge_df is not None and not charge_df.empty:
        st.markdown("---")
        st.subheader("Aperçu Charge Travail Rapide")
        c_cols = st.columns(3)
        with c_cols[0]: display_kpi_metric("Total Opérateurs Actifs", charge_df['NB_OPERATEURS'].sum())
        with c_cols[1]: display_kpi_metric("Heures Dispo./Semaine", f"{charge_df['NB_HEURES_DISPONIBLES_SEMAINE'].sum():.0f}h")
        avg_charge_pct = charge_df['TAUX_CHARGE_POURCENT'].mean() if 'TAUX_CHARGE_POURCENT' in charge_df.columns else 0
        with c_cols[2]: display_kpi_metric("Taux Charge Moyen", f"{avg_charge_pct:.1f}%")


def render_of_details_tab(data: pd.DataFrame):
    st.header("📋 Données Détaillées des Ordres de Fabrication")

    # Advanced Filters
    with st.expander("🔍 Filtres Avancés pour Détail OF", expanded=False):
        col_f1, col_f2, col_f3 = st.columns(3)
        with col_f1:
            familles = ["Toutes"] + sorted(data['FAMILLE_TECHNIQUE'].unique().tolist())
            famille_filter = st.selectbox("Filtrer par Famille Technique", familles, key="of_famille_filter")
        with col_f2:
            clients = ["Tous"] + sorted(data['CLIENT'].unique().tolist())
            client_filter = st.selectbox("Filtrer par Client", clients, key="of_client_filter")
        with col_f3:
            alerte_options = {"Toutes": None, "Avec Alerte": True, "Sans Alerte": False}
            selected_alerte_label = st.selectbox("Filtrer par Alerte Temps", list(alerte_options.keys()), key="of_alerte_filter")
            alerte_param = alerte_options[selected_alerte_label]

    # Apply filters
    filtered_data = data.copy()
    if famille_filter != "Toutes":
        filtered_data = filtered_data[filtered_data['FAMILLE_TECHNIQUE'] == famille_filter]
    if client_filter != "Tous":
        filtered_data = filtered_data[filtered_data['CLIENT'] == client_filter]
    if alerte_param is not None:
        filtered_data = filtered_data[filtered_data['Alerte_temps'] == alerte_param]

    if filtered_data.empty:
        st.info("Aucune donnée OF ne correspond à vos filtres avancés.")
        return

    display_data = filtered_data.copy()
    # Formatting for display
    format_dict = {
        'Avancement_PROD': '{:.1%}',
        'Avancement_temps': '{:.1%}',
        'EFFICACITE': '{:.2f}',
        'Alerte_temps': lambda x: "⚠️ Oui" if x else "✅ Non"
    }
    for col, fmt in format_dict.items():
        if col in display_data.columns:
            if callable(fmt):
                 display_data[col] = display_data[col].apply(fmt)
            else:
                display_data[col] = display_data[col].apply(lambda x: fmt.format(x) if pd.notnull(x) else '')


    columns_to_display = [
        'NUMERO_OFDA', 'PRODUIT', 'DESIGNATION', 'STATUT', 'CLIENT', 'FAMILLE_TECHNIQUE',
        'LANCEMENT_AU_PLUS_TARD', 'QUANTITE_DEMANDEE', 'CUMUL_ENTREES', 'Avancement_PROD',
        'DUREE_PREVUE', 'CUMUL_TEMPS_PASSES', 'Avancement_temps', 'EFFICACITE', 'Alerte_temps'
    ]
    st.dataframe(display_data[columns_to_display], height=500, use_container_width=True)

    csv = filtered_data.to_csv(index=False, encoding='utf-8-sig') # utf-8-sig for Excel accents
    st.download_button(label="📥 Télécharger Détail OF en CSV", data=csv,
                       file_name=f"detail_of_{date.today()}.csv", mime="text/csv")


def render_charts_tab(data: pd.DataFrame):
    st.header("📈 Analyse Graphique de la Production")
    if data.empty:
        st.info("Aucune donnée OF pour les graphiques.")
        return

    plot_template = "plotly_white" # or "seaborn", "ggplot2", "plotly_dark"

    col_c1, col_c2 = st.columns(2)
    with col_c1:
        st.subheader("Avancement Production vs Temps")
        fig_scatter = px.scatter(
            data, x='Avancement_PROD', y='Avancement_temps', color='STATUT',
            hover_data=['NUMERO_OFDA', 'PRODUIT', 'EFFICACITE'],
            title="Corrélation Avancement Production et Temps",
            labels={'Avancement_PROD': 'Avancement Production (%)', 'Avancement_temps': 'Avancement Temps (%)'},
            template=plot_template
        )
        fig_scatter.add_shape(type="line", x0=0, y0=0, x1=1, y1=1, line=dict(color="rgba(255,0,0,0.5)", dash="dash"))
        fig_scatter.update_layout(xaxis_tickformat=".0%", yaxis_tickformat=".0%")
        st.plotly_chart(fig_scatter, use_container_width=True)

    with col_c2:
        st.subheader("Distribution de l'Efficacité")
        fig_hist_eff = px.histogram(
            data[data['EFFICACITE'] > 0], x='EFFICACITE', nbins=30,
            title="Distribution de l'Efficacité des OF",
            labels={'EFFICACITE': 'Efficacité (Prévu/Passé)'},
            template=plot_template
        )
        st.plotly_chart(fig_hist_eff, use_container_width=True)

    st.markdown("---")
    st.subheader("Analyse par Famille Technique")
    if 'FAMILLE_TECHNIQUE' in data.columns and not data['FAMILLE_TECHNIQUE'].empty:
        famille_agg = data.groupby('FAMILLE_TECHNIQUE').agg(
            NB_OF=('NUMERO_OFDA', 'count'),
            AV_PROD_MOY=('Avancement_PROD', 'mean'),
            AV_TEMPS_MOY=('Avancement_temps', 'mean'),
            NB_ALERTES=('Alerte_temps', 'sum')
        ).reset_index()

        fig_famille = go.Figure(data=[
            go.Bar(name='Avancement Prod. Moyen', x=famille_agg['FAMILLE_TECHNIQUE'], y=famille_agg['AV_PROD_MOY'], yaxis='y', offsetgroup=1),
            go.Bar(name='Avancement Temps Moyen', x=famille_agg['FAMILLE_TECHNIQUE'], y=famille_agg['AV_TEMPS_MOY'], yaxis='y', offsetgroup=2),
            go.Bar(name='Nb OF en Alerte', x=famille_agg['FAMILLE_TECHNIQUE'], y=famille_agg['NB_ALERTES'], yaxis='y2', offsetgroup=3, marker_color='orangered')
        ])
        fig_famille.update_layout(
            title_text="Performance par Famille Technique", template=plot_template,
            yaxis=dict(title='Avancement Moyen (%)', tickformat=".0%"),
            yaxis2=dict(title='Nombre d\'Alertes', overlaying='y', side='right', showgrid=False),
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1),
            barmode='group'
        )
        st.plotly_chart(fig_famille, use_container_width=True)
    else:
        st.info("Colonne 'FAMILLE_TECHNIQUE' non disponible pour cette analyse.")


def render_report_tab(data: pd.DataFrame, analyzer: ExcaliburDataAnalyzer):
    st.header("📄 Rapport Textuel de Synthèse")
    if data.empty:
        st.info("Aucune donnée OF pour générer le rapport.")
        return

    report_text = analyzer.generate_summary_report(data)
    st.text_area("Rapport", report_text, height=500)
    st.download_button(label="📥 Télécharger Rapport (.txt)", data=report_text,
                       file_name=f"rapport_production_{date.today()}.txt", mime="text/plain")


def render_charge_tab(charge_data: pd.DataFrame):
    st.header("⚙️ Analyse de la Charge de Travail")
    if charge_data.empty:
        st.info("Aucune donnée de charge de travail disponible.")
        return

    cols_charge = st.columns(3)
    with cols_charge[0]: display_kpi_metric("Secteurs Analysés", len(charge_data))
    with cols_charge[1]: display_kpi_metric("Total Opérateurs", f"{charge_data['NB_OPERATEURS'].sum():.0f}")
    with cols_charge[2]: display_kpi_metric("Heures Disponibles/Sem.", f"{charge_data['NB_HEURES_DISPONIBLES_SEMAINE'].sum():.0f}h")

    st.dataframe(charge_data.style.format({'TAUX_CHARGE_POURCENT': "{:.1f}%"}), use_container_width=True)

    # Create color mapping based on charge percentage
    def get_charge_color(percentage):
        if percentage > 100:
            return 'red'
        elif percentage > 80:
            return 'orange'
        elif percentage > 60:
            return 'yellow'
        else:
            return 'green'

    charge_data_sorted = charge_data.sort_values('TAUX_CHARGE_POURCENT', ascending=False)
    colors = [get_charge_color(pct) for pct in charge_data_sorted['TAUX_CHARGE_POURCENT']]

    fig_charge = px.bar(
        charge_data_sorted,
        x='SECTEUR', y='TAUX_CHARGE_POURCENT',
        color=colors,
        title="Taux de Charge par Secteur (%)",
        labels={'TAUX_CHARGE_POURCENT': 'Taux de Charge (%)'},
        template="plotly_white"
    )
    st.plotly_chart(fig_charge, use_container_width=True)


def render_backlog_tab(backlog_data: pd.DataFrame):
    st.header("⏳ Analyse du Backlog de Production")
    if backlog_data.empty:
        st.info("Aucune donnée de backlog disponible.")
        return

    cols_backlog = st.columns(4)
    with cols_backlog[0]: display_kpi_metric("Total OF en Backlog", len(backlog_data))
    with cols_backlog[1]: display_kpi_metric("OF Urgents", len(backlog_data[backlog_data['PRIORITE'] == 'URGENT']))
    with cols_backlog[2]: display_kpi_metric("Qté Totale Restante", f"{backlog_data['QUANTITE_RESTANTE'].sum():.0f}")
    with cols_backlog[3]: display_kpi_metric("Temps Total Estimé Restant", f"{backlog_data['TEMPS_RESTANT_ESTIME'].sum():.1f}h")

    # Further filters for backlog
    with st.expander("🔍 Filtres pour Backlog", expanded=False):
      f_col1, f_col2 = st.columns(2)
      with f_col1:
          priorities = ["Toutes"] + sorted(backlog_data['PRIORITE'].unique().tolist())
          sel_prio = st.multiselect("Filtrer par Priorité", priorities, default=[], key="backlog_prio_filter")
      with f_col2:
          clients_backlog = ["Tous"] + sorted(backlog_data['CLIENT'].dropna().unique().tolist())
          sel_client = st.multiselect("Filtrer par Client (Backlog)", clients_backlog, default=[], key="backlog_client_filter")

    display_backlog = backlog_data.copy()
    if sel_prio: display_backlog = display_backlog[display_backlog['PRIORITE'].isin(sel_prio)]
    if sel_client: display_backlog = display_backlog[display_backlog['CLIENT'].isin(sel_client)]


    # Display formatted backlog
    display_backlog_formatted = display_backlog.copy()
    display_backlog_formatted['RETARD_JOURS'] = display_backlog_formatted['RETARD_JOURS'].apply(lambda x: f"{x} j" if x > 0 else "-")
    display_backlog_formatted['TEMPS_RESTANT_ESTIME'] = display_backlog_formatted['TEMPS_RESTANT_ESTIME'].apply(lambda x: f"{x:.1f}h")

    def highlight_priority(row):
        color = ''
        if row.PRIORITE == 'URGENT': color = 'background-color: mistyrose'
        elif row.PRIORITE == 'PRIORITAIRE': color = 'background-color: lightyellow'
        return [color] * len(row)

    st.dataframe(display_backlog_formatted.style.apply(highlight_priority, axis=1), use_container_width=True)

    col_b_charts1, col_b_charts2 = st.columns(2)
    with col_b_charts1:
        st.subheader("Répartition par Priorité")
        prio_counts = backlog_data['PRIORITE'].value_counts()
        fig_prio = px.pie(prio_counts, values=prio_counts.values, names=prio_counts.index,
                          title="Priorités du Backlog", hole=0.3, template="seaborn",
                          color_discrete_map={'URGENT': 'orangered', 'PRIORITAIRE': 'gold', 'NORMAL': 'mediumseagreen'})
        st.plotly_chart(fig_prio, use_container_width=True)
    with col_b_charts2:
        st.subheader("Retard en Jours (OF Urgents)")
        urgent_backlog = backlog_data[backlog_data['PRIORITE'] == 'URGENT']
        if not urgent_backlog.empty:
            fig_retard = px.histogram(urgent_backlog, x='RETARD_JOURS', nbins=10,
                                    title="Distribution des Retards (OF Urgents)", template="seaborn")
            st.plotly_chart(fig_retard, use_container_width=True)
        else:
            st.info("Aucun OF urgent dans le backlog.")


def render_personnel_tab(personnel_data: pd.DataFrame):
    st.header("👥 Analyse du Personnel Actif")
    if personnel_data.empty:
        st.info("Aucune donnée de personnel disponible.")
        return

    cols_perso = st.columns(3)
    with cols_perso[0]: display_kpi_metric("Total Employés Actifs", len(personnel_data))

    try:
        # Convert to numeric if needed and handle potential string values
        if 'COEFFICIENT_EFFICACITE' in personnel_data.columns:
            personnel_data['COEFFICIENT_EFFICACITE'] = pd.to_numeric(personnel_data['COEFFICIENT_EFFICACITE'], errors='coerce')
            avg_eff_coeff = personnel_data['COEFFICIENT_EFFICACITE'].mean()
        else:
            avg_eff_coeff = 0
    except Exception as e:
        st.error(f"Erreur lors du calcul du coefficient d'efficacité: {e}")
        avg_eff_coeff = 0

    with cols_perso[1]: display_kpi_metric("Coeff. Efficacité Moyen", f"{avg_eff_coeff:.2f}")
    with cols_perso[2]: display_kpi_metric("Qualifications Uniques", personnel_data['QUALIFICATION'].nunique())

    st.dataframe(personnel_data, use_container_width=True)

    col_p_charts1, col_p_charts2 = st.columns(2)
    with col_p_charts1:
        st.subheader("Répartition par Qualification")
        qual_counts = personnel_data['QUALIFICATION'].value_counts().nlargest(10) # Top 10 for clarity
        fig_qual = px.bar(qual_counts, x=qual_counts.index, y=qual_counts.values,
                          title="Top 10 Qualifications", template="seaborn",
                          labels={'index': 'Qualification', 'value': 'Nombre Employés'})
        st.plotly_chart(fig_qual, use_container_width=True)
    with col_p_charts2:
        st.subheader("Distribution Coeff. Efficacité")
        fig_eff_coeff = px.histogram(personnel_data, x='COEFFICIENT_EFFICACITE', nbins=10,
                                     title="Distribution des Coefficients d'Efficacité", template="seaborn")
        st.plotly_chart(fig_eff_coeff, use_container_width=True)


# --- Entry Point ---
if __name__ == "__main__":
    main()