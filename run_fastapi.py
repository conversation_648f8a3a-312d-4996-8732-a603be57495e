#!/usr/bin/env python3
"""
Script to run the FastAPI application for Production Time Tracking - Excalibur ERP
"""

import subprocess
import sys
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    # Map pip package names to their import names
    required_packages = {
        'fastapi': 'fastapi',
        'uvicorn': 'uvicorn',
        'jinja2': 'jinja2',
        'pandas': 'pandas',
        'pyodbc': 'pyodbc',
        'python-dotenv': 'dotenv'  # pip name vs import name
    }

    missing_packages = []

    for pip_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(pip_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "-r", "requirements.txt"
            ])
            print("✅ Dependencies installed successfully!")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False
    
    return True

def check_environment():
    """Check if .env file exists and contains required variables."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("⚠️ .env file not found. Creating a template...")
        
        env_template = """# Database Configuration for Excalibur ERP
DB_UID=gpao
DB_PWD=flat
DB_HOST=*************:2638
DB_SERVER_NAME=excalib
DB_DATABASE_NAME=excalib

# Optional: Application Configuration
APP_HOST=localhost
APP_PORT=8000
APP_RELOAD=true
"""
        
        with open(".env", "w") as f:
            f.write(env_template)
        
        print("✅ .env template created. Please update with your database credentials.")
        print("📝 Default values are set for Excalibur ERP connection.")
    
    return True

def run_application():
    """Run the FastAPI application."""
    print("🚀 Starting FastAPI Production Tracking Application...")
    print("📊 Dashboard will be available at: http://localhost:8000")
    print("📚 API Documentation at: http://localhost:8000/docs")
    print("🔄 Press Ctrl+C to stop the application")
    print("-" * 60)
    
    try:
        # Import uvicorn here to ensure it's available
        import uvicorn
        
        # Run the application
        uvicorn.run(
            "main:app",
            host="localhost",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {e}")
        return False
    
    return True

def main():
    """Main function to set up and run the application."""
    print("🏭 Excalibur ERP - Production Time Tracking (FastAPI)")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ main.py not found. Please run this script from the project directory.")
        return False
    
    # Check environment setup
    if not check_environment():
        return False
    
    # Check and install dependencies
    if not check_dependencies():
        return False
    
    # Run the application
    return run_application()

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
