#!/usr/bin/env python3
"""
Simple test script to start the FastAPI server
"""

import uvicorn
from main import app

if __name__ == "__main__":
    print("🚀 Starting FastAPI server for testing...")
    print("📊 Dashboard will be available at: http://localhost:8001")
    print("📚 API Documentation at: http://localhost:8001/docs")
    print("🔄 Press Ctrl+C to stop")
    print("-" * 60)

    try:
        uvicorn.run(
            app,
            host="localhost",
            port=8001,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error: {e}")
